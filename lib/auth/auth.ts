import { createClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';
import { authLogger } from '@/lib/utils/logger';

export interface AuthUser {
  id: number;
  username: string | null;
  first_name: string | null;
  is_admin: boolean;
  can_link_topics: boolean;
  can_manage_all_topics: boolean;
}

export class AuthManager {
  
  /**
   * Authenticate a user with Telegram user ID and a simple token
   * This is a basic auth system - in production you'd want proper OAuth
   */
  async authenticateUser(userId: number, authToken: string): Promise<AuthUser | null> {
    authLogger.info(`Attempting to authenticate user ${userId}`, { user_id: userId });

    try {
      // Simple token validation - in production use proper JWT or OAuth
      const expectedToken = this.generateUserToken(userId);
      if (authToken !== expectedToken) {
        console.log(`[Auth] Invalid token for user ${userId}`);
        return null;
      }

      const supabase = await createClient();
      
      // Get user permissions
      const { data: permission } = await supabase
        .from('BuddyIntels_topic_permissions')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (!permission) {
        // Check if user exists in linked topics (has used the bot)
        const { data: userTopics } = await supabase
          .from('BuddyIntels_linked_topics')
          .select('linked_by_user_id, linked_by_username, linked_by_first_name')
          .eq('linked_by_user_id', userId)
          .limit(1);

        if (userTopics && userTopics.length > 0) {
          const userInfo = userTopics[0];
          // Create default permissions for existing user
          const { data: newPermission } = await supabase
            .from('BuddyIntels_topic_permissions')
            .insert({
              user_id: userId,
              username: userInfo.linked_by_username,
              first_name: userInfo.linked_by_first_name,
              is_admin: false,
              can_link_topics: true,
              can_manage_all_topics: false
            })
            .select()
            .single();

          if (newPermission) {
            console.log(`[Auth] Created default permissions for user ${userId}`);
            return {
              id: userId,
              username: newPermission.username,
              first_name: newPermission.first_name,
              is_admin: newPermission.is_admin,
              can_link_topics: newPermission.can_link_topics,
              can_manage_all_topics: newPermission.can_manage_all_topics
            };
          }
        }

        console.log(`[Auth] No permissions found for user ${userId}`);
        return null;
      }

      console.log(`[Auth] Successfully authenticated user ${userId}`);
      return {
        id: userId,
        username: permission.username,
        first_name: permission.first_name,
        is_admin: permission.is_admin,
        can_link_topics: permission.can_link_topics,
        can_manage_all_topics: permission.can_manage_all_topics
      };

    } catch (error) {
      console.error(`[Auth] Error authenticating user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Generate a simple token for a user
   * In production, use proper JWT or OAuth tokens
   */
  generateUserToken(userId: number): string {
    const secret = process.env.AUTH_SECRET || 'default-secret-change-in-production';
    const data = `${userId}-${secret}`;
    
    // Simple hash - in production use proper crypto
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * Set authentication cookie
   */
  async setAuthCookie(userId: number): Promise<void> {
    const cookieStore = await cookies();
    const token = this.generateUserToken(userId);
    
    cookieStore.set('auth_user_id', userId.toString(), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7 // 7 days
    });
    
    cookieStore.set('auth_token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7 // 7 days
    });
  }

  /**
   * Get current authenticated user from cookies
   */
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const cookieStore = await cookies();
      const userIdCookie = cookieStore.get('auth_user_id');
      const tokenCookie = cookieStore.get('auth_token');

      if (!userIdCookie || !tokenCookie) {
        return null;
      }

      const userId = parseInt(userIdCookie.value);
      const token = tokenCookie.value;

      return await this.authenticateUser(userId, token);
    } catch (error) {
      console.error('[Auth] Error getting current user:', error);
      return null;
    }
  }

  /**
   * Clear authentication cookies
   */
  async clearAuthCookies(): Promise<void> {
    const cookieStore = await cookies();
    cookieStore.delete('auth_user_id');
    cookieStore.delete('auth_token');
  }

  /**
   * Check if user has admin permissions
   */
  async isAdmin(userId: number): Promise<boolean> {
    try {
      const supabase = await createClient();
      const { data: permission } = await supabase
        .from('BuddyIntels_topic_permissions')
        .select('is_admin')
        .eq('user_id', userId)
        .single();

      return permission?.is_admin || false;
    } catch (error) {
      console.error(`[Auth] Error checking admin status for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Check if user can manage topics
   */
  async canManageTopics(userId: number): Promise<boolean> {
    try {
      const supabase = await createClient();
      const { data: permission } = await supabase
        .from('BuddyIntels_topic_permissions')
        .select('is_admin, can_link_topics, can_manage_all_topics')
        .eq('user_id', userId)
        .single();

      if (permission) {
        return permission.is_admin || permission.can_link_topics || permission.can_manage_all_topics;
      }

      // Check if linking is open to all users
      const { data: config } = await supabase
        .from('BuddyIntels_bot_config')
        .select('value')
        .eq('key', 'require_admin_for_linking')
        .single();

      return config?.value !== 'true';
    } catch (error) {
      console.error(`[Auth] Error checking topic management permissions for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Create or update user permissions
   */
  async updateUserPermissions(
    userId: number,
    username: string | null,
    firstName: string | null,
    permissions: {
      is_admin?: boolean;
      can_link_topics?: boolean;
      can_manage_all_topics?: boolean;
    }
  ): Promise<boolean> {
    try {
      const supabase = await createClient();
      
      const { error } = await supabase
        .from('BuddyIntels_topic_permissions')
        .upsert({
          user_id: userId,
          username: username,
          first_name: firstName,
          is_admin: permissions.is_admin ?? false,
          can_link_topics: permissions.can_link_topics ?? true,
          can_manage_all_topics: permissions.can_manage_all_topics ?? false,
          updated_at: new Date().toISOString()
        });

      if (error) {
        console.error(`[Auth] Error updating permissions for user ${userId}:`, error);
        return false;
      }

      console.log(`[Auth] Updated permissions for user ${userId}`);
      return true;
    } catch (error) {
      console.error(`[Auth] Error updating permissions for user ${userId}:`, error);
      return false;
    }
  }
}

export const authManager = new AuthManager();
