import { Bot, Context, NextFunction } from 'grammy';
import { twitterClient, extractTweetUrls, extractTweetId, Tweet } from '@/lib/twitter';
import { aiSummarizer } from '@/lib/ai';
import { configManager } from './config';
import { rateLimiter } from './rate-limiter';
import { topicManager } from './topic-manager';
import { commandRecorder, withCommandTiming } from './command-recorder';
import { createClient } from '@/lib/supabase/server';

export class TelegramBot {
  private bot: Bot;
  private processingQueue = new Map<string, Promise<void>>();

  constructor(token: string) {
    this.bot = new Bot(token);
    this.setupMiddleware();
    this.setupHandlers();
  }

  private setupMiddleware() {
    // Command recording middleware (should be first to catch all commands)
    this.bot.use(commandRecorder.createMiddleware());

    // Error handling middleware
    this.bot.catch(async (err) => {
      console.error('Bot error:', err);
      await this.logError('bot_error', err.message, err.stack, {
        update: err.ctx?.update,
        chat_id: err.ctx?.chat?.id,
        user_id: err.ctx?.from?.id
      });
    });

    // Rate limiting middleware
    this.bot.use(async (ctx: Context, next: NextFunction) => {
      if (!ctx.from) return;

      const config = await configManager.getBotConfig();
      const canProceed = await rateLimiter.checkRateLimit(
        ctx.from.id.toString(),
        config.rateLimitPerMinute
      );

      if (!canProceed) {
        const resetTime = await rateLimiter.getResetTime(ctx.from.id.toString());
        await ctx.reply(
          `⏰ Rate limit exceeded. Please wait ${Math.ceil(resetTime / 1000)} seconds before sending another request.`,
          { reply_to_message_id: ctx.message?.message_id }
        );
        return;
      }

      await next();
    });
  }

  private setupHandlers() {
    // Start command
    this.bot.command('start', withCommandTiming('start', async (ctx) => {
      const user = ctx.from;
      const chat = ctx.chat;

      const welcomeMessage =
        `🤖 **Welcome to BuddyIntels!**\n\n` +
        `Hello ${user?.first_name || 'there'}! I'm your Twitter context summarizer bot.\n\n` +

        `🚀 **Quick Start:**\n` +
        `1. Use \`/link\` to register this ${chat?.type === 'private' ? 'chat' : 'group/topic'} for monitoring\n` +
        `2. Send any message with a Twitter/X URL\n` +
        `3. Get instant AI-powered context summaries!\n\n` +

        `📱 **Essential Commands:**\n` +
        `• \`/help\` - Detailed usage guide with examples\n` +
        `• \`/link\` - Enable Twitter URL monitoring here\n` +
        `• \`/status\` - Check your rate limits and bot status\n` +
        `• \`/info\` - Show current chat/topic information\n\n` +

        `🔗 **Supported URLs:**\n` +
        `• twitter.com/user/status/123\n` +
        `• x.com/user/status/123\n\n` +

        `⚡ **Features:**\n` +
        `• Thread analysis & context extraction\n` +
        `• AI-powered summaries with key insights\n` +
        `• 24-hour caching for faster responses\n` +
        `• Rate limiting (5 requests/minute)\n` +
        `• Topic-based monitoring\n\n` +

        `Need help? Use \`/help\` for detailed instructions!`;

      await ctx.reply(welcomeMessage, { parse_mode: 'Markdown' });
    }));

    // Help command
    this.bot.command('help', withCommandTiming('help', async (ctx) => {
      const helpMessage =
        `📚 **BuddyIntels - Complete Usage Guide**\n\n` +

        `🎯 **How It Works:**\n` +
        `1. **Link your chat:** Use \`/link\` to enable monitoring\n` +
        `2. **Share Twitter URLs:** Send any message with Twitter/X links\n` +
        `3. **Get summaries:** Receive AI-powered context analysis\n\n` +

        `🔗 **Supported URL Formats:**\n` +
        `✅ \`https://twitter.com/username/status/123456\`\n` +
        `✅ \`https://x.com/username/status/123456\`\n` +
        `✅ \`twitter.com/username/status/123456\`\n` +
        `✅ \`x.com/username/status/123456\`\n\n` +

        `📱 **Essential Commands:**\n` +
        `• \`/start\` - Welcome message and quick setup\n` +
        `• \`/help\` - This detailed guide\n` +
        `• \`/link\` - Enable Twitter monitoring for this chat\n` +
        `• \`/status\` - Check rate limits and bot health\n` +
        `• \`/info\` - Show current chat/topic details\n\n` +

        `🔧 **Topic Management:**\n` +
        `• \`/unlink\` - Disable monitoring for this chat\n` +
        `• \`/mytopics\` - List all your linked topics\n` +
        `• \`/topics\` - List all linked topics (admin only)\n` +
        `• \`/admin\` - Admin dashboard (admin only)\n\n` +

        `💡 **Example Usage:**\n` +
        `\`\`\`\n` +
        `User: "Check this out: https://x.com/elonmusk/status/123"\n\n` +
        `Bot: "🧵 Tweet Summary\n` +
        `📝 Main Tweet: [content]\n` +
        `🔍 Context: Part of 3-tweet thread about...\n` +
        `💬 Key Replies: 15 responses discussing...\n` +
        `📊 Summary: [AI analysis]"\n` +
        `\`\`\`\n\n` +

        `⚙️ **Features & Limits:**\n` +
        `• **Rate Limit:** 5 requests per minute per user\n` +
        `• **Caching:** 24-hour cache for faster responses\n` +
        `• **Thread Analysis:** Analyzes full conversation context\n` +
        `• **Smart Summaries:** AI extracts key insights\n` +
        `• **Multi-Group:** Works in multiple groups/topics\n\n` +

        `🚨 **Troubleshooting:**\n` +
        `• **Not responding?** Check if chat is linked with \`/info\`\n` +
        `• **Rate limited?** Wait 60 seconds or check \`/status\`\n` +
        `• **URL not working?** Ensure it's a public tweet\n` +
        `• **Need admin help?** Contact admins via \`/admin\`\n\n` +

        `🔒 **Privacy & Security:**\n` +
        `• Only processes public tweets\n` +
        `• Respects Twitter's rate limits\n` +
        `• Logs commands for analytics (anonymous)\n` +
        `• No personal data stored beyond Telegram IDs\n\n` +

        `Need more help? Contact an admin or check \`/status\` for diagnostics!`;

      await ctx.reply(helpMessage, { parse_mode: 'Markdown' });
    }));

    // Status command
    this.bot.command('status', withCommandTiming('status', async (ctx) => {
      try {
        const config = await configManager.getBotConfig();
        const remaining = await rateLimiter.getRemainingRequests(
          ctx.from!.id.toString(),
          config.rateLimitPerMinute
        );

        const activeTopics = await topicManager.getActiveTopics();

        await ctx.reply(
          `🟢 **Bot Status: Online**\n\n` +
          `📊 **Your Rate Limit:**\n` +
          `• Remaining requests: ${remaining}/${config.rateLimitPerMinute}\n` +
          `• Reset time: ${config.rateLimitPerMinute} per minute\n\n` +
          `⚙️ **Configuration:**\n` +
          `• Cache duration: ${config.summaryCacheHours}h\n` +
          `• Max context depth: ${config.maxContextDepth}\n` +
          `• Active topics: ${activeTopics.length}\n\n` +
          `💾 **Database:** Connected`,
          { parse_mode: 'Markdown' }
        );
      } catch {
        await ctx.reply('❌ Error checking status. Please try again.');
      }
    }));

    // Link command - Link current topic for monitoring
    this.bot.command('link', withCommandTiming('link', async (ctx) => {
      await this.handleLinkCommand(ctx);
    }));

    // Unlink command - Unlink current topic
    this.bot.command('unlink', withCommandTiming('unlink', async (ctx) => {
      await this.handleUnlinkCommand(ctx);
    }));

    // Topics command - List all linked topics
    this.bot.command('topics', withCommandTiming('topics', async (ctx) => {
      await this.handleTopicsCommand(ctx);
    }));

    // My topics command - List topics linked by the current user
    this.bot.command('mytopics', withCommandTiming('mytopics', async (ctx) => {
      await this.handleMyTopicsCommand(ctx);
    }));

    // Admin commands
    this.bot.command('admin', withCommandTiming('admin', async (ctx) => {
      await this.handleAdminCommand(ctx);
    }));

    // Info command - Show info about current chat/topic
    this.bot.command('info', withCommandTiming('info', async (ctx) => {
      await this.handleInfoCommand(ctx);
    }));

    // Message handler for tweet URLs
    this.bot.on('message:text', async (ctx) => {
      await this.handleTweetMessage(ctx);
    });
  }

  private async handleTweetMessage(ctx: Context) {
    const message = ctx.message;
    if (!message?.text || !ctx.from) return;

    // Check if message is from an allowed topic using the new topic management system
    const isAllowed = await topicManager.isMessageFromAllowedTopic(
      message.chat.id,
      message.message_thread_id || null
    );

    if (!isAllowed) {
      console.log(`[Bot] Message from unauthorized topic: chatId=${message.chat.id}, topicId=${message.message_thread_id}`);
      return; // Silently ignore messages from unauthorized groups/topics
    }

    // Extract tweet URLs
    const tweetUrls = extractTweetUrls(message.text);
    if (tweetUrls.length === 0) return;

    // Process first tweet URL found
    const tweetUrl = tweetUrls[0];
    const tweetId = extractTweetId(tweetUrl);
    if (!tweetId) return;

    // Check if already processing this message
    const messageKey = `${message.chat.id}:${message.message_id}`;
    if (this.processingQueue.has(messageKey)) {
      return;
    }

    // Check if message was already processed
    const alreadyProcessed = await this.isMessageProcessed(
      message.message_id,
      message.chat.id
    );
    if (alreadyProcessed) {
      return;
    }

    // Add to processing queue
    const processingPromise = this.processTweetUrl(ctx, tweetId, tweetUrl);
    this.processingQueue.set(messageKey, processingPromise);

    try {
      await processingPromise;
    } finally {
      this.processingQueue.delete(messageKey);
    }
  }

  private async processTweetUrl(ctx: Context, tweetId: string, tweetUrl: string) {
    const message = ctx.message!;
    const user = ctx.from!;
    
    // Send "typing" indicator
    await ctx.replyWithChatAction('typing');

    try {
      // Check cache first
      const cached = await twitterClient.getCachedSummary(tweetId);
      if (cached) {
        const replyText = await aiSummarizer.generateReplyText(
          { id: tweetId, text: cached.text, author: { username: 'cached', name: 'Cached', id: 'cached' } } as Tweet,
          { summary: cached.summary, key_points: [], sentiment: 'neutral', participants: [] }
        );
        
        await ctx.reply(replyText, {
          reply_to_message_id: message.message_id,
          parse_mode: 'Markdown'
        });
        return;
      }

      // Fetch tweet context
      const config = await configManager.getBotConfig();
      const tweets = await twitterClient.fetchTweetContext(tweetId, config.maxContextDepth);
      
      if (tweets.length === 0) {
        await ctx.reply(
          '❌ Sorry, I couldn\'t fetch this tweet. It might be private, deleted, or there was an API error.',
          { reply_to_message_id: message.message_id }
        );
        return;
      }

      // Get the original tweet
      const originalTweet = tweets.find(t => t.id === tweetId) || tweets[0];

      // Generate summary
      const summary = await aiSummarizer.summarizeContext(tweets);
      
      // Cache the summary
      await twitterClient.cacheSummary(tweetId, originalTweet.text, summary.summary, tweets);

      // Generate and send reply
      const replyText = await aiSummarizer.generateReplyText(originalTweet, summary);
      await ctx.reply(replyText, {
        reply_to_message_id: message.message_id,
        parse_mode: 'Markdown'
      });

      // Mark message as processed
      await this.markMessageProcessed(message.message_id, message.chat.id, user.id, tweetUrl);

    } catch (error) {
      console.error('Error processing tweet:', error);
      await this.logError('tweet_processing_error', 
        error instanceof Error ? error.message : 'Unknown error', 
        error instanceof Error ? error.stack : undefined, 
        {
          tweet_id: tweetId,
          tweet_url: tweetUrl,
          user_id: user.id,
          chat_id: message.chat.id
        });

      await ctx.reply(
        '❌ Sorry, I encountered an error while processing this tweet. Please try again later.',
        { reply_to_message_id: message.message_id }
      );
    }
  }

  private async isMessageProcessed(messageId: number, chatId: number): Promise<boolean> {
    try {
      const supabase = await createClient();
      const { data, error } = await supabase
        .from('BuddyIntels_processed_messages')
        .select('id')
        .eq('message_id', messageId)
        .eq('chat_id', chatId)
        .single();

      return !error && data !== null;
    } catch (error) {
      console.error('Error checking processed message:', error);
      return false;
    }
  }

  private async markMessageProcessed(messageId: number, chatId: number, userId: number, tweetUrl: string): Promise<void> {
    try {
      const supabase = await createClient();
      await supabase
        .from('BuddyIntels_processed_messages')
        .insert({
          message_id: messageId,
          chat_id: chatId,
          user_id: userId,
          tweet_url: tweetUrl
        });
    } catch (error) {
      console.error('Error marking message as processed:', error);
    }
  }

  private async handleLinkCommand(ctx: Context): Promise<void> {
    const message = ctx.message;
    const user = ctx.from;

    if (!message || !user) return;

    console.log(`[Bot] Link command from user ${user.id} in chat ${message.chat.id}, topic ${message.message_thread_id}`);

    try {
      // Get chat and topic information
      const groupId = message.chat.id;
      const topicId = message.message_thread_id || null;

      // Get chat title
      let groupTitle: string | null = null;
      let topicTitle: string | null = null;

      if (message.chat.type === 'group' || message.chat.type === 'supergroup') {
        groupTitle = message.chat.title || null;
      }

      // For topics, we'll try to get the topic name from the message
      if (topicId && message.reply_to_message?.forum_topic_created) {
        topicTitle = message.reply_to_message.forum_topic_created.name;
      }

      const result = await topicManager.linkTopic(
        groupId,
        topicId,
        groupTitle,
        topicTitle,
        user.id,
        user.username || null,
        user.first_name || null
      );

      if (result.success) {
        const locationText = topicId
          ? `topic "${topicTitle || 'Unknown Topic'}" in group "${groupTitle || 'Unknown Group'}"`
          : `group "${groupTitle || 'Unknown Group'}"`;

        await ctx.reply(
          `✅ ${result.message}\n\n` +
          `📍 **Linked Location:** ${locationText}\n` +
          `👤 **Linked by:** ${user.first_name}${user.username ? ` (@${user.username})` : ''}\n\n` +
          `Now I'll monitor this ${topicId ? 'topic' : 'group'} for Twitter URLs and provide summaries!`,
          { parse_mode: 'Markdown' }
        );
      } else {
        await ctx.reply(`❌ ${result.message}`);
      }

    } catch (error) {
      console.error('[Bot] Error in link command:', error);
      await this.logError('link_command_error',
        error instanceof Error ? error.message : 'Unknown error',
        error instanceof Error ? error.stack : undefined,
        {
          user_id: user.id,
          chat_id: message.chat.id,
          topic_id: message.message_thread_id
        });

      await ctx.reply('❌ An error occurred while linking this topic. Please try again.');
    }
  }

  private async handleUnlinkCommand(ctx: Context): Promise<void> {
    const message = ctx.message;
    const user = ctx.from;

    if (!message || !user) return;

    console.log(`[Bot] Unlink command from user ${user.id} in chat ${message.chat.id}, topic ${message.message_thread_id}`);

    try {
      const groupId = message.chat.id;
      const topicId = message.message_thread_id || null;

      const result = await topicManager.unlinkTopic(
        groupId,
        topicId,
        user.id,
        user.username || null
      );

      if (result.success) {
        await ctx.reply(`✅ ${result.message}`);
      } else {
        await ctx.reply(`❌ ${result.message}`);
      }

    } catch (error) {
      console.error('[Bot] Error in unlink command:', error);
      await this.logError('unlink_command_error',
        error instanceof Error ? error.message : 'Unknown error',
        error instanceof Error ? error.stack : undefined,
        {
          user_id: user.id,
          chat_id: message.chat.id,
          topic_id: message.message_thread_id
        });

      await ctx.reply('❌ An error occurred while unlinking this topic. Please try again.');
    }
  }

  private async handleTopicsCommand(ctx: Context): Promise<void> {
    const user = ctx.from;

    if (!user) return;

    console.log(`[Bot] Topics command from user ${user.id}`);

    try {
      const activeTopics = await topicManager.getActiveTopics();

      if (activeTopics.length === 0) {
        await ctx.reply(
          '📋 **Linked Topics**\n\n' +
          'No topics are currently linked for monitoring.\n\n' +
          'Use `/link` in a group or topic to start monitoring it for Twitter URLs.',
          { parse_mode: 'Markdown' }
        );
        return;
      }

      let topicsText = '📋 **Linked Topics**\n\n';

      activeTopics.forEach((topic, index) => {
        const location = topic.topic_id
          ? `📌 ${topic.topic_title || 'Unknown Topic'} (in ${topic.group_title || 'Unknown Group'})`
          : `🏠 ${topic.group_title || 'Unknown Group'}`;

        const linkedBy = topic.linked_by_first_name +
          (topic.linked_by_username ? ` (@${topic.linked_by_username})` : '');

        const createdDate = new Date(topic.created_at).toLocaleDateString();

        topicsText += `${index + 1}. ${location}\n`;
        topicsText += `   👤 Linked by: ${linkedBy}\n`;
        topicsText += `   📅 Created: ${createdDate}\n\n`;
      });

      topicsText += `Total: ${activeTopics.length} active topic${activeTopics.length === 1 ? '' : 's'}`;

      await ctx.reply(topicsText, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('[Bot] Error in topics command:', error);
      await this.logError('topics_command_error',
        error instanceof Error ? error.message : 'Unknown error',
        error instanceof Error ? error.stack : undefined,
        {
          user_id: user.id
        });

      await ctx.reply('❌ An error occurred while fetching topics. Please try again.');
    }
  }

  private async handleMyTopicsCommand(ctx: Context): Promise<void> {
    const user = ctx.from;

    if (!user) return;

    console.log(`[Bot] MyTopics command from user ${user.id}`);

    try {
      const supabase = await createClient();
      const { data: userTopics, error } = await supabase
        .from('BuddyIntels_linked_topics')
        .select('*')
        .eq('linked_by_user_id', user.id)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;

      if (userTopics.length === 0) {
        await ctx.reply(
          '📋 **Your Linked Topics**\n\n' +
          'You haven\'t linked any topics yet.\n\n' +
          'Use `/link` in a group or topic to start monitoring it for Twitter URLs.',
          { parse_mode: 'Markdown' }
        );
        return;
      }

      let topicsText = '📋 **Your Linked Topics**\n\n';

      userTopics.forEach((topic, index) => {
        const location = topic.topic_id
          ? `📌 ${topic.topic_title || 'Unknown Topic'} (in ${topic.group_title || 'Unknown Group'})`
          : `🏠 ${topic.group_title || 'Unknown Group'}`;

        const createdDate = new Date(topic.created_at).toLocaleDateString();

        topicsText += `${index + 1}. ${location}\n`;
        topicsText += `   📅 Created: ${createdDate}\n`;
        topicsText += `   🆔 ID: ${topic.id}\n\n`;
      });

      topicsText += `Total: ${userTopics.length} active topic${userTopics.length === 1 ? '' : 's'}`;

      await ctx.reply(topicsText, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('[Bot] Error in mytopics command:', error);
      await this.logError('mytopics_command_error',
        error instanceof Error ? error.message : 'Unknown error',
        error instanceof Error ? error.stack : undefined,
        {
          user_id: user.id
        });

      await ctx.reply('❌ An error occurred while fetching your topics. Please try again.');
    }
  }

  private async handleAdminCommand(ctx: Context): Promise<void> {
    const user = ctx.from;

    if (!user) return;

    console.log(`[Bot] Admin command from user ${user.id}`);

    try {
      // Check if user has admin permissions
      const supabase = await createClient();
      const { data: permission } = await supabase
        .from('BuddyIntels_topic_permissions')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (!permission?.is_admin) {
        await ctx.reply('❌ You do not have admin permissions.');
        return;
      }

      const activeTopics = await topicManager.getActiveTopics();
      const totalTopics = activeTopics.length;
      const groupCount = new Set(activeTopics.map(t => t.group_id)).size;

      // Get recent activity
      const { data: recentActivity } = await supabase
        .from('BuddyIntels_topic_activity_logs')
        .select('action, created_at')
        .order('created_at', { ascending: false })
        .limit(10);

      const activitySummary = recentActivity?.reduce((acc: any, log) => {
        acc[log.action] = (acc[log.action] || 0) + 1;
        return acc;
      }, {}) || {};

      let adminText = '🔧 **Admin Dashboard**\n\n';
      adminText += `📊 **Statistics:**\n`;
      adminText += `• Total active topics: ${totalTopics}\n`;
      adminText += `• Unique groups: ${groupCount}\n`;
      adminText += `• Recent activity: ${recentActivity?.length || 0} actions\n\n`;

      if (Object.keys(activitySummary).length > 0) {
        adminText += `📈 **Recent Actions:**\n`;
        Object.entries(activitySummary).forEach(([action, count]) => {
          adminText += `• ${action}: ${count}\n`;
        });
        adminText += '\n';
      }

      adminText += `🛠️ **Admin Commands:**\n`;
      adminText += `• \`/topics\` - View all topics\n`;
      adminText += `• \`/info\` - Get chat/topic info\n`;
      adminText += `• Dashboard: ${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/dashboard`;

      await ctx.reply(adminText, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('[Bot] Error in admin command:', error);
      await this.logError('admin_command_error',
        error instanceof Error ? error.message : 'Unknown error',
        error instanceof Error ? error.stack : undefined,
        {
          user_id: user.id
        });

      await ctx.reply('❌ An error occurred while fetching admin info. Please try again.');
    }
  }

  private async handleInfoCommand(ctx: Context): Promise<void> {
    const message = ctx.message;
    const user = ctx.from;

    if (!message || !user) return;

    console.log(`[Bot] Info command from user ${user.id} in chat ${message.chat.id}`);

    try {
      const groupId = message.chat.id;
      const topicId = message.message_thread_id || null;

      // Check if this topic is linked
      const supabase = await createClient();
      const { data: linkedTopic } = await supabase
        .from('BuddyIntels_linked_topics')
        .select('*')
        .eq('group_id', groupId)
        .eq('topic_id', topicId)
        .single();

      let infoText = '📍 **Chat/Topic Information**\n\n';

      // Chat info
      infoText += `🏠 **Chat Details:**\n`;
      infoText += `• Type: ${message.chat.type}\n`;
      infoText += `• ID: \`${groupId}\`\n`;
      if (message.chat.title) {
        infoText += `• Title: ${message.chat.title}\n`;
      }

      // Topic info
      if (topicId) {
        infoText += `\n📌 **Topic Details:**\n`;
        infoText += `• Topic ID: \`${topicId}\`\n`;
        // Try to get topic name from message context if available
        if (message.reply_to_message?.forum_topic_created) {
          infoText += `• Topic Name: ${message.reply_to_message.forum_topic_created.name}\n`;
        }
      }

      // Linking status
      infoText += `\n🔗 **Monitoring Status:**\n`;
      if (linkedTopic) {
        infoText += `• Status: ✅ Linked and ${linkedTopic.is_active ? 'Active' : 'Inactive'}\n`;
        infoText += `• Linked by: ${linkedTopic.linked_by_first_name}`;
        if (linkedTopic.linked_by_username) {
          infoText += ` (@${linkedTopic.linked_by_username})`;
        }
        infoText += `\n• Linked on: ${new Date(linkedTopic.created_at).toLocaleDateString()}\n`;
        infoText += `• Internal ID: ${linkedTopic.id}\n`;
      } else {
        infoText += `• Status: ❌ Not linked\n`;
        infoText += `• Use \`/link\` to start monitoring this ${topicId ? 'topic' : 'chat'}\n`;
      }

      // User info
      infoText += `\n👤 **Your Info:**\n`;
      infoText += `• User ID: \`${user.id}\`\n`;
      infoText += `• Name: ${user.first_name}`;
      if (user.username) {
        infoText += ` (@${user.username})`;
      }

      await ctx.reply(infoText, { parse_mode: 'Markdown' });

    } catch (error) {
      console.error('[Bot] Error in info command:', error);
      await this.logError('info_command_error',
        error instanceof Error ? error.message : 'Unknown error',
        error instanceof Error ? error.stack : undefined,
        {
          user_id: user.id,
          chat_id: message.chat.id,
          topic_id: message.message_thread_id
        });

      await ctx.reply('❌ An error occurred while fetching info. Please try again.');
    }
  }

  private async logError(errorType: string, message: string, stackTrace?: string, context?: unknown): Promise<void> {
    try {
      const supabase = await createClient();
      await supabase
        .from('BuddyIntels_error_logs')
        .insert({
          error_type: errorType,
          error_message: message,
          stack_trace: stackTrace,
          context: context || {}
        });
    } catch (error) {
      console.error('Error logging to database:', error);
    }
  }

  getBot(): Bot {
    return this.bot;
  }
}

export function createTelegramBot(): TelegramBot {
  const token = process.env.TELEGRAM_BOT_TOKEN;
  if (!token) {
    throw new Error('TELEGRAM_BOT_TOKEN environment variable is required');
  }
  
  return new TelegramBot(token);
}