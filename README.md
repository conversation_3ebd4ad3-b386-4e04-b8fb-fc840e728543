# Telegram Bot - Tweet Context Summarizer

A Telegram bot that analyzes Twitter/X URLs and provides AI-powered context summaries of tweets and their threads.

## Features

- **Tweet Context Analysis**: Fetches tweet threads, replies, quotes, and retweets
- **AI Summarization**: Uses Gemini 2.0 Flash via OpenRouter to generate concise summaries
- **Dynamic Topic Management**: Use `/link` command to register topics for monitoring
- **Web Dashboard**: Manage linked topics through a user-friendly interface
- **User Authentication**: Secure access control with Telegram User ID authentication
- **Rate Limiting**: 5 requests per minute per user
- **Caching**: 24-hour cache for tweet summaries
- **Comprehensive Logging**: Track all activities with detailed logs
- **Error Handling**: Comprehensive error logging and user feedback

## Setup

### 1. Clone and Install

```bash
git clone <repository-url>
cd buddyintels
bun install
```

### 2. Database Setup

1. Create a Supabase project at [supabase.com](https://supabase.com)
2. Run the SQL schema in `supabase/schema.sql` in your Supabase SQL Editor
3. Note your project URL and anon key

### 3. Environment Variables

Copy `.env.example` to `.env.local` and configure:

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=your-supabase-anon-key

# Telegram Bot (create via @BotFather)
TELEGRAM_BOT_TOKEN=your-telegram-bot-token

# Authentication (for dashboard access)
AUTH_SECRET=your-random-secret-key

# OpenRouter (sign up at openrouter.ai)
OPENROUTER_API_KEY=your-openrouter-api-key

# Twitter API (get from Twitter Developer Portal)
TWITTER_API_KEY=your-twitter-api-bearer-token
```

### 4. Development

```bash
bun dev
```

### 5. Production Deployment

#### Deploy to Vercel

1. Connect your repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy

#### Set up Telegram Webhook

Once deployed, set the webhook:

```bash
curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://your-app.vercel.app/api/telegram/webhook"}'
```

## Usage

### 🚀 Quick Start Guide

#### Step 1: Add Bot to Your Group/Channel
1. **Find the bot**: Search for `@YourBotUsername` in Telegram
2. **Add to group**: Click "Add to Group" and select your target group
3. **Grant permissions**: Make sure the bot can read messages and send replies
4. **For topic groups**: The bot works in both regular groups and topic-based groups

#### Step 2: Link Your Group/Topic for Monitoring
```
/link
```
- Run this command in any group or topic you want to monitor
- The bot will register this location for Twitter URL processing
- Only linked groups/topics will process Twitter URLs (security feature)
- You can link multiple groups and topics

#### Step 3: Start Using the Bot
Send any message containing a Twitter/X URL:

**Supported URL formats:**
- `https://twitter.com/username/status/1234567890`
- `https://x.com/username/status/1234567890`
- `twitter.com/username/status/1234567890`
- `x.com/username/status/1234567890`

**Example usage:**
```
Check out this thread: https://x.com/elonmusk/status/1234567890
```

The bot will automatically:
1. ✅ Detect the Twitter URL
2. 🔍 Fetch the tweet and its context (replies, quotes, thread)
3. 🤖 Generate an AI-powered summary
4. 📝 Reply with key insights and context

### 📱 Bot Commands

#### Essential Commands
- `/start` - Welcome message and quick setup guide
- `/help` - Detailed usage instructions and examples
- `/link` - Link current group/topic for monitoring
- `/status` - Check bot status, rate limits, and your usage

#### Topic Management
- `/unlink` - Remove current group/topic from monitoring
- `/mytopics` - List all topics you've linked
- `/topics` - List all linked topics (admin/manager only)
- `/info` - Show current chat/topic information

#### Admin Commands
- `/admin` - Access admin dashboard (admin users only)

### 💡 Usage Examples

#### Basic Tweet Analysis
```
User: "What do you think about this? https://x.com/username/status/123"

Bot: "🧵 **Tweet Summary**

**Main Tweet:** [Original tweet content]

**Key Context:**
• This is part of a 5-tweet thread about AI development
• 23 replies discussing implementation challenges
• Quote tweets from industry experts sharing concerns

**Summary:** The thread discusses recent AI breakthroughs, with community feedback highlighting both excitement and caution about rapid development pace.

⏱️ Processed in 2.3s | 💾 Cached for 24h"
```

#### Topic-Based Monitoring
```
# In a crypto discussion topic
User: "/link"
Bot: "✅ Topic linked! This crypto discussion will now monitor Twitter URLs."

# Later in the same topic
User: "Breaking: https://x.com/coinbase/status/456"
Bot: [Provides detailed analysis of the Coinbase announcement]
```

### ⚙️ Configuration & Limits

#### Rate Limiting
- **5 requests per minute** per user
- Rate limits reset every minute
- Check your remaining requests with `/status`
- Prevents spam and ensures fair usage

#### Caching System
- **24-hour cache** for tweet summaries
- Repeated URLs return cached results instantly
- Reduces API calls and improves response time
- Cache status shown in bot responses

#### Topic Management
- **Unlimited linked topics** per user
- **Admin approval** may be required (configurable)
- **Automatic cleanup** of inactive topics
- **Activity logging** for all topic changes

## Architecture

```
lib/
├── telegram/          # Telegram bot logic
│   ├── bot.ts        # Main bot handlers
│   ├── config.ts     # Configuration management
│   └── rate-limiter.ts # Rate limiting
├── twitter/          # Twitter API integration
│   ├── client.ts     # Twitter API client
│   └── parser.ts     # URL parsing utilities
├── ai/               # AI summarization
│   └── summarizer.ts # OpenRouter/Gemini integration
└── supabase/         # Database clients
```

## Dynamic Topic Management

### Using the `/link` Command

Instead of hardcoding group and topic IDs, you can now dynamically link topics for monitoring:

1. **In any Telegram group or topic**, send `/link`
2. The bot will register that location for Twitter URL monitoring
3. Manage linked topics through the web dashboard

### Bot Commands

| Command | Description |
|---------|-------------|
| `/start` | Show welcome message and available commands |
| `/help` | Display detailed help information |
| `/link` | Link current topic/group for monitoring |
| `/unlink` | Unlink current topic/group |
| `/topics` | List all linked topics (admin/manager only) |
| `/mytopics` | List topics you've linked |
| `/info` | Show current chat/topic information |
| `/status` | Check bot status and your rate limits |
| `/admin` | Admin dashboard (admin only) |

### Web Dashboard

Access the management dashboard at `/dashboard`:

- **View all linked topics** with detailed information
- **Activate/deactivate topics** individually or in bulk
- **Delete topics** that are no longer needed
- **View activity logs** for all topic management actions
- **User authentication** via Telegram User ID

### 📊 Analytics Dashboard

Access comprehensive bot analytics at `/analytics`:

#### **Command Analytics**
- **Usage Statistics**: Track command frequency and performance
- **Response Times**: Monitor bot performance metrics
- **Error Rates**: Identify and troubleshoot issues
- **Rate Limiting**: Track rate limit violations

#### **User Analytics**
- **Top Users**: Most active bot users by command count
- **User Patterns**: Command usage patterns and preferences
- **Activity Tracking**: User engagement over time
- **Permission Analysis**: User role and permission usage

#### **Chat Analytics**
- **Active Chats**: Most engaged groups and channels
- **Chat Performance**: Response times by chat type
- **Usage Distribution**: Command usage across different chats
- **Error Analysis**: Chat-specific error patterns

#### **Management Tools**
- **Data Export**: Export usage data to CSV for analysis
- **Data Cleanup**: Automated cleanup of old analytics data
- **Real-time Dashboard**: Live analytics with auto-refresh
- **Historical Trends**: Track usage patterns over time

#### **CLI Analytics Tools**
```bash
# View comprehensive dashboard
bun run analytics

# Get command statistics for last 7 days
bun run analytics:stats

# Show top 10 users from last 30 days
bun run analytics:users

# Clean up data older than 90 days
bun run analytics:cleanup
```

### User Permissions

The system supports three permission levels:

1. **Admin**: Full access to all topics and system management
2. **Manager**: Can manage all topics but not system settings
3. **User**: Can only link topics and manage their own

### Configuration

Bot configuration is stored in the `BuddyIntels_bot_config` table:

| Key | Default | Description |
|-----|---------|-------------|
| `rate_limit_per_minute` | `5` | Requests per minute per user |
| `summary_cache_hours` | `24` | Cache duration for summaries |
| `max_context_depth` | `5` | Maximum thread depth to analyze |
| `require_admin_for_linking` | `false` | Whether only admins can link topics |
| `auto_detect_topic_names` | `true` | Automatically detect topic names |

## API Endpoints

### Core Endpoints
- `POST /api/telegram/webhook` - Telegram webhook handler
- `GET /api/telegram/webhook` - Bot health check
- `GET /api/health` - Application health check

### Topic Management
- `GET /api/topics` - List linked topics
- `POST /api/topics` - Create new linked topic
- `PATCH /api/topics` - Bulk update topics
- `DELETE /api/topics` - Bulk delete topics
- `GET /api/topics/[id]` - Get specific topic
- `PATCH /api/topics/[id]` - Update specific topic
- `DELETE /api/topics/[id]` - Delete specific topic
- `GET /api/topics/activity` - Get activity logs

### Authentication
- `POST /api/auth/login` - User login
- `GET /api/auth/login` - Check auth status
- `POST /api/auth/logout` - User logout

### Admin
- `GET /api/admin/logs` - System logs (admin only)
- `DELETE /api/admin/logs` - Clear old logs (admin only)

### Analytics
- `GET /api/analytics?type=dashboard` - Comprehensive analytics dashboard
- `GET /api/analytics?type=stats&days=7` - Command usage statistics
- `GET /api/analytics?type=users&limit=10&days=30` - Top users by activity
- `GET /api/analytics?type=chats&limit=10` - Most active chats
- `GET /api/analytics?type=errors&days=7` - Error analysis
- `GET /api/analytics?type=trends&days=30` - Usage trends over time
- `POST /api/analytics` - Data management (cleanup, export)

## Error Handling

- All errors are logged to the `error_logs` table
- Users receive friendly error messages
- Rate limiting prevents abuse
- Duplicate message processing is prevented

## Development

### Database Schema

See `supabase/schema.sql` for the complete database schema including:
- `BuddyIntels_bot_config` - Configuration storage
- `BuddyIntels_linked_topics` - Dynamic topic management
- `BuddyIntels_topic_permissions` - User permissions
- `BuddyIntels_topic_activity_logs` - Activity tracking
- `BuddyIntels_processed_messages` - Duplicate prevention
- `BuddyIntels_tweet_summaries` - Summary caching
- `BuddyIntels_error_logs` - Error tracking

### Adding Features

1. Extend the bot handlers in `lib/telegram/bot.ts`
2. Add new configuration options in `lib/telegram/config.ts`
3. Update the database schema if needed
4. Add appropriate error handling

### 🔧 Troubleshooting & FAQ

#### Common Issues

**🤖 Bot not responding to commands**
- ✅ Verify the bot is added to your group with proper permissions
- ✅ Check if the group/topic is linked with `/link`
- ✅ Ensure you're not hitting rate limits (check with `/status`)
- ✅ Try `/start` to verify bot connectivity

**⏰ Rate limit errors**
```
"⏰ Rate limit exceeded. Please wait 45 seconds..."
```
- **Solution**: Wait for the cooldown period or check `/status`
- **Admin fix**: Adjust `rate_limit_per_minute` in bot configuration
- **Tip**: Spread out your requests over time

**🐦 Twitter URL not being processed**
- ✅ Ensure URL format is correct (twitter.com or x.com)
- ✅ Check if the group/topic is linked (`/info` to verify)
- ✅ Verify the tweet exists and is public
- ✅ Some tweets may be rate-limited by Twitter API

**🔗 Cannot link topic**
```
"❌ You don't have permission to link topics"
```
- **Cause**: Admin-only linking is enabled
- **Solution**: Contact an admin or check permissions with `/admin`
- **Self-fix**: Admins can adjust `require_admin_for_linking` setting

**💾 Database connection errors**
- **Check**: Supabase connection and credentials
- **Verify**: Database schema is properly set up
- **Review**: Error logs in `/api/admin/logs` (admin only)

#### Performance Tips

**🚀 Optimize Response Times**
- Use cached results when possible (24h cache)
- Link only active groups/topics to reduce overhead
- Avoid sending multiple URLs in rapid succession

**📊 Monitor Usage**
- Use `/status` to check your rate limit usage
- Review `/mytopics` to manage your linked topics
- Admins can monitor system health via `/admin`

**🔒 Security Best Practices**
- Only link topics you actively monitor
- Remove inactive topics with `/unlink`
- Report suspicious activity to admins
- Keep bot permissions minimal (read messages, send replies)

#### Getting Help

**📞 Support Channels**
1. **Check bot status**: `/status` for immediate diagnostics
2. **Review documentation**: This README and inline help
3. **Contact admins**: Use `/admin` to find admin contacts
4. **Check logs**: Admins can review error logs for issues

**🐛 Reporting Bugs**
When reporting issues, include:
- Command that failed
- Error message received
- Group/topic information (`/info`)
- Timestamp of the issue
- Your user ID (visible in `/status`)

## Troubleshooting (Technical)

**For Developers and Admins:**

1. **Bot not responding**: Check webhook setup and environment variables
2. **Rate limit errors**: Adjust `rate_limit_per_minute` in bot_config
3. **Twitter API errors**: Verify API key and rate limits
4. **Database errors**: Check Supabase connection and table schema
5. **RLS Policy issues**: Verify JWT tokens contain `telegram_user_id`
6. **Webhook failures**: Check `/api/telegram/webhook` endpoint logs

## License

MIT