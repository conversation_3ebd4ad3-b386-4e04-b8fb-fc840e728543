# Telegram Bot - Tweet Context Summarizer

A Telegram bot that analyzes Twitter/X URLs and provides AI-powered context summaries of tweets and their threads.

## Features

- **Tweet Context Analysis**: Fetches tweet threads, replies, quotes, and retweets
- **AI Summarization**: Uses Gemini 2.0 Flash via OpenRouter to generate concise summaries
- **Dynamic Topic Management**: Use `/link` command to register topics for monitoring
- **Web Dashboard**: Manage linked topics through a user-friendly interface
- **User Authentication**: Secure access control with Telegram User ID authentication
- **Rate Limiting**: 5 requests per minute per user
- **Caching**: 24-hour cache for tweet summaries
- **Comprehensive Logging**: Track all activities with detailed logs
- **Error Handling**: Comprehensive error logging and user feedback

## Setup

### 1. Clone and Install

```bash
git clone <repository-url>
cd buddyintels
bun install
```

### 2. Database Setup

1. Create a Supabase project at [supabase.com](https://supabase.com)
2. Run the SQL schema in `supabase/schema.sql` in your Supabase SQL Editor
3. Note your project URL and anon key

### 3. Environment Variables

Copy `.env.example` to `.env.local` and configure:

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=your-supabase-anon-key

# Telegram Bot (create via @BotFather)
TELEGRAM_BOT_TOKEN=your-telegram-bot-token

# Authentication (for dashboard access)
AUTH_SECRET=your-random-secret-key

# OpenRouter (sign up at openrouter.ai)
OPENROUTER_API_KEY=your-openrouter-api-key

# Twitter API (get from Twitter Developer Portal)
TWITTER_API_KEY=your-twitter-api-bearer-token
```

### 4. Development

```bash
bun dev
```

### 5. Production Deployment

#### Deploy to Vercel

1. Connect your repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy

#### Set up Telegram Webhook

Once deployed, set the webhook:

```bash
curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://your-app.vercel.app/api/telegram/webhook"}'
```

## Usage

1. Add the bot to your Telegram group
2. Send a message containing a Twitter/X URL
3. The bot will reply with a context summary

### Commands

- `/start` - Welcome message
- `/help` - Usage instructions
- `/status` - Check bot status and rate limits

## Architecture

```
lib/
├── telegram/          # Telegram bot logic
│   ├── bot.ts        # Main bot handlers
│   ├── config.ts     # Configuration management
│   └── rate-limiter.ts # Rate limiting
├── twitter/          # Twitter API integration
│   ├── client.ts     # Twitter API client
│   └── parser.ts     # URL parsing utilities
├── ai/               # AI summarization
│   └── summarizer.ts # OpenRouter/Gemini integration
└── supabase/         # Database clients
```

## Dynamic Topic Management

### Using the `/link` Command

Instead of hardcoding group and topic IDs, you can now dynamically link topics for monitoring:

1. **In any Telegram group or topic**, send `/link`
2. The bot will register that location for Twitter URL monitoring
3. Manage linked topics through the web dashboard

### Bot Commands

| Command | Description |
|---------|-------------|
| `/start` | Show welcome message and available commands |
| `/help` | Display detailed help information |
| `/link` | Link current topic/group for monitoring |
| `/unlink` | Unlink current topic/group |
| `/topics` | List all linked topics (admin/manager only) |
| `/mytopics` | List topics you've linked |
| `/info` | Show current chat/topic information |
| `/status` | Check bot status and your rate limits |
| `/admin` | Admin dashboard (admin only) |

### Web Dashboard

Access the management dashboard at `/dashboard`:

- **View all linked topics** with detailed information
- **Activate/deactivate topics** individually or in bulk
- **Delete topics** that are no longer needed
- **View activity logs** for all topic management actions
- **User authentication** via Telegram User ID

### User Permissions

The system supports three permission levels:

1. **Admin**: Full access to all topics and system management
2. **Manager**: Can manage all topics but not system settings
3. **User**: Can only link topics and manage their own

### Configuration

Bot configuration is stored in the `BuddyIntels_bot_config` table:

| Key | Default | Description |
|-----|---------|-------------|
| `rate_limit_per_minute` | `5` | Requests per minute per user |
| `summary_cache_hours` | `24` | Cache duration for summaries |
| `max_context_depth` | `5` | Maximum thread depth to analyze |
| `require_admin_for_linking` | `false` | Whether only admins can link topics |
| `auto_detect_topic_names` | `true` | Automatically detect topic names |

## API Endpoints

### Core Endpoints
- `POST /api/telegram/webhook` - Telegram webhook handler
- `GET /api/telegram/webhook` - Bot health check
- `GET /api/health` - Application health check

### Topic Management
- `GET /api/topics` - List linked topics
- `POST /api/topics` - Create new linked topic
- `PATCH /api/topics` - Bulk update topics
- `DELETE /api/topics` - Bulk delete topics
- `GET /api/topics/[id]` - Get specific topic
- `PATCH /api/topics/[id]` - Update specific topic
- `DELETE /api/topics/[id]` - Delete specific topic
- `GET /api/topics/activity` - Get activity logs

### Authentication
- `POST /api/auth/login` - User login
- `GET /api/auth/login` - Check auth status
- `POST /api/auth/logout` - User logout

### Admin
- `GET /api/admin/logs` - System logs (admin only)
- `DELETE /api/admin/logs` - Clear old logs (admin only)

## Error Handling

- All errors are logged to the `error_logs` table
- Users receive friendly error messages
- Rate limiting prevents abuse
- Duplicate message processing is prevented

## Development

### Database Schema

See `supabase/schema.sql` for the complete database schema including:
- `BuddyIntels_bot_config` - Configuration storage
- `BuddyIntels_linked_topics` - Dynamic topic management
- `BuddyIntels_topic_permissions` - User permissions
- `BuddyIntels_topic_activity_logs` - Activity tracking
- `BuddyIntels_processed_messages` - Duplicate prevention
- `BuddyIntels_tweet_summaries` - Summary caching
- `BuddyIntels_error_logs` - Error tracking

### Adding Features

1. Extend the bot handlers in `lib/telegram/bot.ts`
2. Add new configuration options in `lib/telegram/config.ts`
3. Update the database schema if needed
4. Add appropriate error handling

## Troubleshooting

1. **Bot not responding**: Check webhook setup and environment variables
2. **Rate limit errors**: Adjust `rate_limit_per_minute` in bot_config
3. **Twitter API errors**: Verify API key and rate limits
4. **Database errors**: Check Supabase connection and table schema

## License

MIT