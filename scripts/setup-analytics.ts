#!/usr/bin/env bun
/**
 * Setup Analytics Database Tables
 * 
 * This script sets up the command analytics tables and functions in your Supabase database.
 * Run this after setting up the main schema to enable command tracking.
 * 
 * Usage:
 *   bun run scripts/setup-analytics.ts
 */

import { createClient } from '@/lib/supabase/server';
import { readFileSync } from 'fs';
import { join } from 'path';

async function setupAnalytics() {
  console.log('🚀 Setting up BuddyIntels Analytics...\n');

  try {
    const supabase = await createClient();

    // Read the analytics SQL file
    const sqlPath = join(process.cwd(), 'supabase', 'command_analytics.sql');
    const sql = readFileSync(sqlPath, 'utf-8');

    console.log('📄 Reading analytics schema from:', sqlPath);

    // Split SQL into individual statements
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('/*'));

    console.log(`📊 Found ${statements.length} SQL statements to execute\n`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      // Skip comments and empty statements
      if (statement.startsWith('--') || statement.startsWith('/*') || statement.trim().length === 0) {
        continue;
      }

      try {
        console.log(`⏳ Executing statement ${i + 1}/${statements.length}...`);
        
        const { error } = await supabase.rpc('exec_sql', { sql_query: statement + ';' });
        
        if (error) {
          // Try direct execution if RPC fails
          const { error: directError } = await supabase.from('_').select('*').limit(0);
          if (directError) {
            console.warn(`⚠️  Statement ${i + 1} failed:`, error.message);
            console.log('Statement:', statement.substring(0, 100) + '...');
          }
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`);
        }
      } catch (execError) {
        console.warn(`⚠️  Statement ${i + 1} failed:`, execError);
        console.log('Statement:', statement.substring(0, 100) + '...');
      }
    }

    console.log('\n🎉 Analytics setup complete!');
    console.log('\n📋 What was created:');
    console.log('  ✅ BuddyIntels_command_usage table');
    console.log('  ✅ BuddyIntels_command_stats view');
    console.log('  ✅ BuddyIntels_user_activity view');
    console.log('  ✅ BuddyIntels_chat_activity view');
    console.log('  ✅ Helper functions for analytics');
    console.log('  ✅ Performance indexes');

    console.log('\n🔧 Next steps:');
    console.log('  1. Update your bot code to use the command recorder');
    console.log('  2. Access analytics at /analytics in your web dashboard');
    console.log('  3. Use CLI tools: bun run analytics');
    console.log('  4. Set up RLS policies if needed');

    // Test the setup by checking if tables exist
    console.log('\n🧪 Testing setup...');
    
    const { data: tables, error: tableError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .like('table_name', 'BuddyIntels_command%');

    if (tableError) {
      console.warn('⚠️  Could not verify table creation:', tableError.message);
    } else {
      console.log(`✅ Found ${tables?.length || 0} analytics tables`);
      tables?.forEach(table => {
        console.log(`   - ${table.table_name}`);
      });
    }

    // Test function creation
    const { data: functions, error: funcError } = await supabase
      .from('information_schema.routines')
      .select('routine_name')
      .eq('routine_schema', 'public')
      .in('routine_name', [
        'log_command_usage',
        'get_command_stats',
        'get_top_users',
        'cleanup_old_command_usage'
      ]);

    if (funcError) {
      console.warn('⚠️  Could not verify function creation:', funcError.message);
    } else {
      console.log(`✅ Found ${functions?.length || 0} analytics functions`);
      functions?.forEach(func => {
        console.log(`   - ${func.routine_name}()`);
      });
    }

    console.log('\n🎯 Analytics setup completed successfully!');
    console.log('   You can now start tracking command usage in your bot.');

  } catch (error) {
    console.error('❌ Setup failed:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('  1. Ensure your Supabase connection is working');
    console.log('  2. Check that you have admin permissions');
    console.log('  3. Verify the schema.sql file exists');
    console.log('  4. Try running the SQL manually in Supabase SQL Editor');
    process.exit(1);
  }
}

// Alternative setup using direct SQL execution
async function setupAnalyticsDirectSQL() {
  console.log('🚀 Setting up Analytics with direct SQL execution...\n');

  try {
    const supabase = await createClient();
    const sqlPath = join(process.cwd(), 'supabase', 'command_analytics.sql');
    const sql = readFileSync(sqlPath, 'utf-8');

    console.log('📄 Executing analytics schema...');

    // Execute the entire SQL file at once
    const { error } = await supabase.rpc('exec_sql', { sql_query: sql });

    if (error) {
      throw error;
    }

    console.log('✅ Analytics schema executed successfully!');
    
  } catch (error) {
    console.error('❌ Direct SQL setup failed:', error);
    console.log('\n💡 Try running the SQL file manually in Supabase SQL Editor:');
    console.log('   1. Open your Supabase dashboard');
    console.log('   2. Go to SQL Editor');
    console.log('   3. Copy and paste the contents of supabase/command_analytics.sql');
    console.log('   4. Run the query');
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const method = args[0] || 'default';

  switch (method) {
    case 'direct':
      await setupAnalyticsDirectSQL();
      break;
    case 'default':
    default:
      await setupAnalytics();
      break;
  }
}

// Run if called directly
if (import.meta.main) {
  main();
}
