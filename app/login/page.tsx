import { Metadata } from "next";
import { LoginForm } from "@/components/auth/login-form";
import { authManager } from "@/lib/auth/auth";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Login - Topic Management Dashboard",
  description: "Login to access the topic management dashboard",
};

export default async function LoginPage() {
  // Check if user is already authenticated
  const currentUser = await authManager.getCurrentUser();
  if (currentUser) {
    redirect('/dashboard');
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Topic Management Dashboard
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Sign in to manage your Telegram topics
          </p>
        </div>
        <LoginForm />
      </div>
    </div>
  );
}
