'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  Users, 
  MessageSquare, 
  AlertTriangle, 
  Download, 
  Trash2,
  RefreshCw,
  TrendingUp
} from 'lucide-react';

interface DashboardData {
  stats: any[];
  users: any[];
  chats: any[];
  errors: any[];
  timestamp: string;
}

export default function AnalyticsPage() {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboard = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/analytics?type=dashboard');
      const result = await response.json();
      
      if (result.success) {
        setData(result.data);
        setError(null);
      } else {
        setError(result.error || 'Failed to fetch data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const handleCleanup = async () => {
    if (!confirm('Are you sure you want to clean up old data (90+ days)?')) return;
    
    try {
      const response = await fetch('/api/analytics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'cleanup', daysToKeep: 90 })
      });
      
      const result = await response.json();
      if (result.success) {
        alert(`Cleanup complete: ${result.deletedCount} records deleted`);
        fetchDashboard();
      } else {
        alert(`Cleanup failed: ${result.error}`);
      }
    } catch (err) {
      alert(`Cleanup error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const handleExport = async () => {
    try {
      const response = await fetch('/api/analytics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'export', days: 30 })
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `command-usage-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        const result = await response.json();
        alert(`Export failed: ${result.error}`);
      }
    } catch (err) {
      alert(`Export error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  useEffect(() => {
    fetchDashboard();
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading analytics...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="text-red-600">Error Loading Analytics</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={fetchDashboard} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">BuddyIntels Analytics</h1>
          <p className="text-muted-foreground">
            Command usage analytics and bot performance metrics
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={fetchDashboard} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={handleExport} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button onClick={handleCleanup} variant="outline" size="sm">
            <Trash2 className="h-4 w-4 mr-2" />
            Cleanup
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Commands</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data?.stats?.reduce((sum, stat) => sum + stat.total_uses, 0) || 0}
            </div>
            <p className="text-xs text-muted-foreground">Last 7 days</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data?.users?.length || 0}</div>
            <p className="text-xs text-muted-foreground">Last 30 days</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Chats</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data?.chats?.length || 0}</div>
            <p className="text-xs text-muted-foreground">All time</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data?.errors?.length ? `${data.errors.length}` : '0'}
            </div>
            <p className="text-xs text-muted-foreground">Last 7 days</p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="commands" className="space-y-4">
        <TabsList>
          <TabsTrigger value="commands">Commands</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="chats">Chats</TabsTrigger>
          <TabsTrigger value="errors">Errors</TabsTrigger>
        </TabsList>

        <TabsContent value="commands" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Command Usage (Last 7 days)</CardTitle>
              <CardDescription>Most frequently used bot commands</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data?.stats?.map((stat, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded">
                    <div className="flex items-center space-x-3">
                      <Badge variant="outline">/{stat.command}</Badge>
                      <div>
                        <p className="font-medium">{stat.total_uses} uses</p>
                        <p className="text-sm text-muted-foreground">
                          {stat.unique_users} users • {Math.round(stat.avg_response_time_ms || 0)}ms avg
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm">
                        {(stat.error_rate || 0).toFixed(1)}% errors
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {(stat.rate_limit_rate || 0).toFixed(1)}% rate limited
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Top Users (Last 30 days)</CardTitle>
              <CardDescription>Most active bot users</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data?.users?.map((user, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <p className="font-medium">
                        {user.first_name || 'Unknown'} 
                        {user.username && ` (@${user.username})`}
                      </p>
                      <p className="text-sm text-muted-foreground">ID: {user.user_id}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{user.total_commands} commands</p>
                      <p className="text-sm text-muted-foreground">
                        {user.unique_commands} unique commands
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="chats" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Most Active Chats</CardTitle>
              <CardDescription>Groups and channels with highest bot usage</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data?.chats?.map((chat, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <p className="font-medium">{chat.chat_title || 'Unnamed Chat'}</p>
                      <p className="text-sm text-muted-foreground">
                        {chat.chat_type} • ID: {chat.chat_id}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{chat.total_commands} commands</p>
                      <p className="text-sm text-muted-foreground">
                        {chat.unique_users} users • {chat.error_count} errors
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="errors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Error Analysis (Last 7 days)</CardTitle>
              <CardDescription>Most common errors and issues</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data?.errors?.length ? (
                  data.errors.map((error, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded border-red-200">
                      <div className="flex-1">
                        <p className="font-medium text-red-600">/{error.command}</p>
                        <p className="text-sm text-muted-foreground">
                          {error.error_message?.substring(0, 100)}...
                        </p>
                      </div>
                      <Badge variant="destructive">{error.error_count}</Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                    <p>No errors in the last 7 days! 🎉</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Footer */}
      <div className="text-center text-sm text-muted-foreground">
        Last updated: {data?.timestamp ? new Date(data.timestamp).toLocaleString() : 'Never'}
      </div>
    </div>
  );
}
