import { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { Shield, ArrowLeft } from "lucide-react";

export const metadata: Metadata = {
  title: "Unauthorized - Topic Management Dashboard",
  description: "You don't have permission to access this resource",
};

export default function UnauthorizedPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <Shield className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-2xl">Access Denied</CardTitle>
            <CardDescription>
              You don't have permission to access the topic management dashboard
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-gray-600 space-y-2">
              <p>
                To access the dashboard, you need to:
              </p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Have used the Telegram bot at least once (linked a topic)</li>
                <li>Have been granted permissions by an administrator</li>
                <li>Be logged in with the correct Telegram User ID</li>
              </ul>
            </div>
            
            <div className="pt-4 space-y-2">
              <Link href="/login" className="block">
                <Button className="w-full">
                  Try Different Account
                </Button>
              </Link>
              
              <Link href="/" className="block">
                <Button variant="outline" className="w-full">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Home
                </Button>
              </Link>
            </div>
            
            <div className="pt-4 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">Need Access?</h3>
              <p className="text-sm text-blue-800">
                Contact your administrator or use the Telegram bot first by sending 
                the <code>/link</code> command in a group or topic you want to monitor.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
