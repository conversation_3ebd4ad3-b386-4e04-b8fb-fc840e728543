import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'dashboard';
    const days = parseInt(searchParams.get('days') || '7');
    const limit = parseInt(searchParams.get('limit') || '10');

    const supabase = await createClient();

    switch (type) {
      case 'stats': {
        const { data, error } = await supabase
          .rpc('get_command_stats', {
            start_date: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString(),
            end_date: new Date().toISOString()
          });

        if (error) throw error;
        return NextResponse.json({ success: true, data });
      }

      case 'users': {
        const { data, error } = await supabase
          .rpc('get_top_users', {
            limit_count: limit,
            start_date: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()
          });

        if (error) throw error;
        return NextResponse.json({ success: true, data });
      }

      case 'chats': {
        const { data, error } = await supabase
          .from('BuddyIntels_chat_activity')
          .select('*')
          .order('total_commands', { ascending: false })
          .limit(limit);

        if (error) throw error;
        return NextResponse.json({ success: true, data });
      }

      case 'errors': {
        const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString();
        
        const { data, error } = await supabase
          .from('BuddyIntels_command_usage')
          .select('command, error_message, COUNT(*) as error_count')
          .eq('success', false)
          .gte('created_at', startDate)
          .group('command, error_message')
          .order('error_count', { ascending: false })
          .limit(limit);

        if (error) throw error;
        return NextResponse.json({ success: true, data });
      }

      case 'trends': {
        const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString();
        
        const { data, error } = await supabase
          .from('BuddyIntels_command_usage')
          .select(`
            DATE_TRUNC('day', created_at) as date,
            command,
            COUNT(*) as count,
            COUNT(DISTINCT user_id) as unique_users,
            AVG(response_time_ms) as avg_response_time
          `)
          .gte('created_at', startDate)
          .group('DATE_TRUNC(\'day\', created_at), command')
          .order('date', { ascending: true });

        if (error) throw error;
        return NextResponse.json({ success: true, data });
      }

      case 'dashboard':
      default: {
        // Get comprehensive dashboard data
        const [statsResult, usersResult, chatsResult, errorsResult] = await Promise.allSettled([
          supabase.rpc('get_command_stats', {
            start_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            end_date: new Date().toISOString()
          }),
          supabase.rpc('get_top_users', {
            limit_count: 10,
            start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
          }),
          supabase.from('BuddyIntels_chat_activity')
            .select('*')
            .order('total_commands', { ascending: false })
            .limit(10),
          supabase.from('BuddyIntels_command_usage')
            .select('command, error_message, COUNT(*) as error_count')
            .eq('success', false)
            .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
            .group('command, error_message')
            .order('error_count', { ascending: false })
            .limit(10)
        ]);

        const dashboard = {
          stats: statsResult.status === 'fulfilled' ? statsResult.value.data : [],
          users: usersResult.status === 'fulfilled' ? usersResult.value.data : [],
          chats: chatsResult.status === 'fulfilled' ? chatsResult.value.data : [],
          errors: errorsResult.status === 'fulfilled' ? errorsResult.value.data : [],
          timestamp: new Date().toISOString()
        };

        return NextResponse.json({ success: true, data: dashboard });
      }
    }
  } catch (error) {
    console.error('Analytics API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action, ...params } = await request.json();
    const supabase = await createClient();

    switch (action) {
      case 'cleanup': {
        const daysToKeep = params.daysToKeep || 90;
        const { data, error } = await supabase
          .rpc('cleanup_old_command_usage', {
            days_to_keep: daysToKeep
          });

        if (error) throw error;
        
        return NextResponse.json({ 
          success: true, 
          message: `Cleaned up old data, deleted ${data} records`,
          deletedCount: data 
        });
      }

      case 'export': {
        const days = params.days || 30;
        const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString();
        
        const { data, error } = await supabase
          .from('BuddyIntels_command_usage')
          .select('*')
          .gte('created_at', startDate)
          .order('created_at', { ascending: false });

        if (error) throw error;

        // Convert to CSV format
        if (!data || data.length === 0) {
          return NextResponse.json({ 
            success: false, 
            error: 'No data to export' 
          });
        }

        const headers = Object.keys(data[0]).join(',');
        const rows = data.map(row => 
          Object.values(row).map(value => 
            typeof value === 'string' && value.includes(',') 
              ? `"${value.replace(/"/g, '""')}"` 
              : value
          ).join(',')
        );

        const csv = [headers, ...rows].join('\n');
        
        return new NextResponse(csv, {
          headers: {
            'Content-Type': 'text/csv',
            'Content-Disposition': `attachment; filename="command-usage-${new Date().toISOString().split('T')[0]}.csv"`
          }
        });
      }

      default:
        return NextResponse.json(
          { success: false, error: 'Unknown action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Analytics POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
