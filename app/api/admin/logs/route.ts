import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { authManager } from '@/lib/auth/auth';
import { apiLogger } from '@/lib/utils/logger';

// GET /api/admin/logs - Get system logs (admin only)
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  apiLogger.info('GET /api/admin/logs - Fetching system logs');
  
  try {
    // Check authentication and admin permissions
    const currentUser = await authManager.getCurrentUser();
    if (!currentUser) {
      apiLogger.warn('Unauthorized access attempt to admin logs');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!currentUser.is_admin) {
      apiLogger.warn(`Non-admin user ${currentUser.id} attempted to access logs`);
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const errorType = searchParams.get('type');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    const supabase = await createClient();
    
    let query = supabase
      .from('BuddyIntels_error_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (errorType) {
      query = query.eq('error_type', errorType);
    }

    if (startDate) {
      query = query.gte('created_at', startDate);
    }

    if (endDate) {
      query = query.lte('created_at', endDate);
    }

    const { data: logs, error, count } = await query;

    if (error) {
      apiLogger.error('Error fetching logs', error, { user_id: currentUser.id });
      return NextResponse.json(
        { error: 'Failed to fetch logs' },
        { status: 500 }
      );
    }

    // Get log statistics
    const { data: stats } = await supabase
      .from('BuddyIntels_error_logs')
      .select('error_type, created_at')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

    const logStats = stats?.reduce((acc: any, log) => {
      acc[log.error_type] = (acc[log.error_type] || 0) + 1;
      return acc;
    }, {}) || {};

    const duration = Date.now() - startTime;
    apiLogger.logApiRequest('GET', '/api/admin/logs', currentUser.id, 200, duration);

    return NextResponse.json({
      success: true,
      data: logs || [],
      count: logs?.length || 0,
      total: count,
      stats: logStats,
      pagination: {
        limit,
        offset,
        hasMore: (logs?.length || 0) === limit
      }
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    apiLogger.error('Unexpected error in GET /api/admin/logs', error as Error);
    apiLogger.logApiRequest('GET', '/api/admin/logs', undefined, 500, duration);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/logs - Clear old logs (admin only)
export async function DELETE(request: NextRequest) {
  const startTime = Date.now();
  apiLogger.info('DELETE /api/admin/logs - Clearing old logs');
  
  try {
    // Check authentication and admin permissions
    const currentUser = await authManager.getCurrentUser();
    if (!currentUser) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!currentUser.is_admin) {
      apiLogger.warn(`Non-admin user ${currentUser.id} attempted to clear logs`);
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { days = 30 } = body;

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const supabase = await createClient();
    
    const { error, count } = await supabase
      .from('BuddyIntels_error_logs')
      .delete()
      .lt('created_at', cutoffDate.toISOString());

    if (error) {
      apiLogger.error('Error clearing logs', error, { user_id: currentUser.id });
      return NextResponse.json(
        { error: 'Failed to clear logs' },
        { status: 500 }
      );
    }

    const duration = Date.now() - startTime;
    apiLogger.info(`Cleared ${count || 0} log entries older than ${days} days`, {
      user_id: currentUser.id,
      days: days,
      deleted_count: count
    });
    apiLogger.logApiRequest('DELETE', '/api/admin/logs', currentUser.id, 200, duration);

    return NextResponse.json({
      success: true,
      message: `Cleared ${count || 0} log entries older than ${days} days`,
      deleted_count: count || 0
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    apiLogger.error('Unexpected error in DELETE /api/admin/logs', error as Error);
    apiLogger.logApiRequest('DELETE', '/api/admin/logs', undefined, 500, duration);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
