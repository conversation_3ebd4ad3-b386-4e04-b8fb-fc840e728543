import { Metada<PERSON> } from "next";
import { createClient } from "@/lib/supabase/server";
import { authManager } from "@/lib/auth/auth";
import { TopicDashboard } from "@/components/dashboard/topic-dashboard";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Topic Management Dashboard",
  description: "Manage Telegram topics for Twitter URL monitoring",
};

export default async function DashboardPage() {
  // Check authentication
  const currentUser = await authManager.getCurrentUser();
  if (!currentUser) {
    redirect('/login');
  }

  // Check if user can manage topics
  const canManage = await authManager.canManageTopics(currentUser.id);
  if (!canManage) {
    redirect('/unauthorized');
  }

  const supabase = await createClient();

  // Fetch topics based on user permissions
  let topicsQuery = supabase
    .from('BuddyIntels_linked_topics')
    .select('*')
    .order('created_at', { ascending: false });

  // If user is not admin and can't manage all topics, only show their topics
  if (!currentUser.is_admin && !currentUser.can_manage_all_topics) {
    topicsQuery = topicsQuery.eq('linked_by_user_id', currentUser.id);
  }

  const { data: topics } = await topicsQuery;

  // Fetch recent activity
  let activityQuery = supabase
    .from('BuddyIntels_topic_activity_logs')
    .select(`
      *,
      BuddyIntels_linked_topics(
        id,
        group_title,
        topic_title
      )
    `)
    .order('created_at', { ascending: false })
    .limit(10);

  // If user is not admin, only show activity for their topics
  if (!currentUser.is_admin && !currentUser.can_manage_all_topics) {
    activityQuery = activityQuery.eq('user_id', currentUser.id);
  }

  const { data: recentActivity } = await activityQuery;

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader user={currentUser} />

      <div className="container mx-auto py-10">
        <TopicDashboard
          initialTopics={topics || []}
          recentActivity={recentActivity || []}
          currentUser={currentUser}
        />
      </div>
    </div>
  );
}
