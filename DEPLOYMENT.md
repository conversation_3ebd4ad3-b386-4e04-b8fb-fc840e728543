# BuddyIntels Deployment Guide

## 🚀 Quick Deployment Checklist

### 1. Environment Variables Setup

Ensure these environment variables are configured in your production environment:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://ojxlpqerellfuiiidddj.supabase.co
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=your-supabase-anon-key

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_WEBHOOK_SECRET=your-webhook-secret-optional
TELEGRAM_GROUP_ID=
TELEGRAM_TOPIC_ID=

# OpenRouter API Configuration
OPENROUTER_API_KEY=your-openrouter-api-key

# Twitter API Configuration
TWITTER_API_KEY=your-twitter-api-key

# Production URL for webhook setup
NEXT_PUBLIC_APP_URL=https://buddyintels.oddofrancesco.com
```

### 2. Database Setup

Ensure your Supabase database has the required tables. Run the SQL migrations:

```bash
# Check if tables exist
bun run analytics:stats

# If tables don't exist, they should be auto-created by the bot
```

### 3. Webhook Configuration

The webhook has been configured for: `https://buddyintels.oddofrancesco.com/api/telegram/webhook`

To verify or reconfigure:

```bash
# Check current webhook status
bun run webhook:info

# Reconfigure webhook if needed
bun run webhook:setup

# Delete webhook (if needed)
bun run webhook:delete
```

### 4. Deployment Commands

```bash
# Build for production
bun run build

# Start production server
bun run start

# Or deploy to your hosting platform
```

### 5. Testing the Bot

1. **Find your bot on Telegram**: Search for your bot using the username from @BotFather
2. **Start the bot**: Send `/start` command
3. **Link a group/topic**: 
   - Add the bot to a group
   - Use `/link` command in the group or topic
4. **Test with a tweet**: Send a message with a Twitter/X URL

### 6. Monitoring

- **Health check**: `GET https://buddyintels.oddofrancesco.com/api/telegram/webhook`
- **Dashboard**: `https://buddyintels.oddofrancesco.com/dashboard` (if admin)
- **Logs**: Check your hosting platform's logs for errors

### 7. Common Issues

#### Webhook 500 Error
- Check that all environment variables are set
- Verify Supabase connection
- Check application logs

#### Bot not responding
- Verify webhook is set: `bun run webhook:info`
- Check if group/topic is linked: Use `/info` command
- Verify bot has necessary permissions in the group

#### Rate limiting
- Users can check status with `/status` command
- Default limit: 5 requests per minute per user

### 8. Security Notes

- Keep your `TELEGRAM_BOT_TOKEN` secure
- Use `TELEGRAM_WEBHOOK_SECRET` for webhook verification
- Regularly rotate API keys
- Monitor for unusual activity in dashboard

## 📱 Bot Commands Reference

### User Commands
- `/start` - Welcome message and setup guide
- `/help` - Detailed usage instructions
- `/link` - Enable monitoring for current chat/topic
- `/unlink` - Disable monitoring for current chat/topic
- `/status` - Check rate limits and bot status
- `/info` - Show current chat/topic information
- `/mytopics` - List topics you've linked

### Admin Commands
- `/topics` - List all linked topics
- `/admin` - Admin dashboard access

## 🔧 Maintenance

### Update Bot Token
1. Get new token from @BotFather
2. Update `TELEGRAM_BOT_TOKEN` environment variable
3. Restart the application
4. Run `bun run webhook:setup` to reconfigure

### Database Cleanup
```bash
# View analytics
bun run analytics:stats

# Cleanup old data (if implemented)
bun run analytics:cleanup
```

### Logs Analysis
Check your hosting platform's logs for:
- Webhook errors
- API rate limits
- Database connection issues
- AI summarization failures

---

## 📞 Support

If you encounter issues:
1. Check the deployment logs
2. Verify environment variables
3. Test webhook with `bun run webhook:info`
4. Use bot's `/status` command for diagnostics