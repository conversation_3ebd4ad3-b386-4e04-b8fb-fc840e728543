{"name": "buddyintels", "version": "1.0.0", "description": "BuddyIntels: AI-Powered Twitter Context Bot for Telegram", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "analytics": "bun run scripts/command-analytics.ts", "analytics:stats": "bun run scripts/command-analytics.ts stats", "analytics:users": "bun run scripts/command-analytics.ts users", "analytics:cleanup": "bun run scripts/command-analytics.ts cleanup", "setup:analytics": "bun run scripts/setup-analytics.ts", "webhook:setup": "node scripts/setup-webhook.js", "webhook:info": "node scripts/setup-webhook.js --info", "webhook:delete": "node scripts/setup-webhook.js --delete"}, "dependencies": {"@ai-sdk/openai": "^1.3.23", "@grammyjs/types": "^3.21.0", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "ai": "^4.3.19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "grammy": "^1.37.0", "lucide-react": "^0.511.0", "next": "latest", "next-themes": "^0.4.6", "p-queue": "^8.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^24.0.14", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.3.1", "postcss": "^8", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}